import 'package:get/get.dart';
import 'package:rolio/common/constants/error_codes.dart';
import 'package:rolio/common/constants/string_constants.dart';
import 'package:rolio/common/interfaces/role_provider.dart';
import 'package:rolio/common/models/page_request.dart';
import 'package:rolio/common/utils/error_handler.dart';
import 'package:rolio/common/utils/logger.dart';
import 'package:rolio/common/models/ai_role.dart';
import 'package:rolio/modules/role/repository/recommend_repository.dart';
import 'package:rolio/common/cache/cache_manager.dart';
import 'package:rolio/common/services/session_binding_service.dart';

/// 推荐服务，实现了IRoleProvider接口
/// 
/// 负责处理AI角色数据的业务逻辑，包括数据加载、缓存管理和角色查询
class RecommendService extends GetxService implements IRoleProvider {
  // AI角色列表
  final RxList<AiRole> recommendedRoles = <AiRole>[].obs;
  
  // 会话角色列表（缓存）
  final RxList<AiRole> sessionRoles = <AiRole>[].obs;
  
  // 仓库实例
  final RecommendRepository _repository;
  
  // 缓存管理器
  final CacheManager _cacheManager;
  
  // 角色加载锁 - 防止并发请求
  final RxBool isLoadingRoles = false.obs;
  
  // 分页相关
  bool hasMoreData = true;
  int _totalCount = 0;
  
  // 获取总数据量的getter
  int get totalCount => _totalCount;
  
  // 保存当前已加载的最大页码，用于状态恢复
  int _maxLoadedPage = 1;
  
  // 正在进行的API请求，用于跟踪和调试
  final RxString currentApiRequest = RxString('');
  
  // 缓存键前缀
  static const String _cacheKeyPrefix = 'role_';
  static const String _avatarCacheKeyPrefix = 'role_avatar_';
  static const String _coverCacheKeyPrefix = 'role_cover_';
  
  // 热门数据是否已预加载
  final RxBool isHotDataPreloaded = false.obs;
  
  // 构造函数 - 通过依赖注入获取仓库实例和缓存管理器
  RecommendService({RecommendRepository? repository})
      : _repository = repository ?? Get.find<RecommendRepository>(),
        _cacheManager = Get.find<CacheManager>() {
    LogUtil.debug('RecommendService 构造函数执行，使用依赖注入获取仓库实例');
  }
  
  @override
  void onInit() {
    super.onInit();
    // 初始化时使用默认角色
    LogUtil.info('RecommendService初始化');
    _useMockData();
    
  }
  
  // 异步初始化角色数据
  Future<void> _initializeRoles() async {
    try {
      // 使用计算隔离加载数据，避免阻塞主线程
      LogUtil.debug('RecommendService: 开始异步加载推荐角色，强制刷新');
      
      // 创建初始分页请求
      final initialRequest = PageRequest(page: 1, size: StringsConsts.recommendPageSize);
      
      // 执行刷新操作，首次加载务必强制刷新
      final success = await getPagedRoles(initialRequest, forceRefresh: true);
      
      if (success) {
        LogUtil.info('RecommendService: 推荐角色加载完成，共${recommendedRoles.length}个角色');
      } else {
        LogUtil.warn('RecommendService: 推荐角色加载失败，使用模拟数据');
        // 如果加载失败，使用模拟数据而不是重试
        if (recommendedRoles.isEmpty) {
          _useMockData();
        }
      }
    } catch (e) {
      LogUtil.error('RecommendService: 初始化角色数据失败: $e');
      // 确保至少有默认角色可用
      if (recommendedRoles.isEmpty) {
        _useMockData();
      }
    }
  }
  
  // 使用模拟数据
  void _useMockData() {
    LogUtil.info('使用空的AI角色列表，不显示模拟数据');
    // 使用空列表代替模拟数据
    if (recommendedRoles.isEmpty) {
      recommendedRoles.assignAll([]);
    }
  }
  
  /// 从仓库加载推荐角色列表（分页）
  /// 
  /// [request] 分页请求参数
  /// [forceRefresh] 是否强制刷新缓存
  /// [isLoadMore] 是否为加载更多操作
  /// 返回是否成功
  Future<bool> getPagedRoles(PageRequest request, {bool forceRefresh = false, bool isLoadMore = false}) async {
    LogUtil.info('=== 开始加载推荐角色（分页） ===');
    LogUtil.info('page: ${request.page}, size: ${request.size}, forceRefresh: $forceRefresh, isLoadMore: $isLoadMore');
    
    // 防止并发请求
    if (isLoadingRoles.value && !isLoadMore) {
      LogUtil.debug('已有加载任务在进行中，跳过重复请求');
      return false;
    }
    
    try {
      // 只有在非加载更多时才设置全局加载状态
      if (!isLoadMore) {
        isLoadingRoles.value = true;
      }
      
      // 如果强制刷新，先清除仓库缓存
      if (forceRefresh) {
        LogUtil.debug('强制刷新，清除仓库缓存');
        await _repository.clearCache();
        // 重置最大页码
        _maxLoadedPage = 1;
        // 重置hasMoreData，避免因缓存失效导致错误判断
        hasMoreData = true;
      }
      
      // 从仓库获取角色列表，使用PageRequest参数
      final params = request.toParams();
      params['tag'] = StringsConsts.recommendDefaultTag; // 添加标签参数
      
      LogUtil.debug('请求参数: $params');
      
      // 记录当前API请求用于调试
      currentApiRequest.value = 'getList with params: $params';
      
      // 调用仓库层方法获取数据，传递forceRefresh参数
      final result = await _repository.getPagedList(
        params: params,
        forceRefresh: forceRefresh
      );
      
      final roles = result.items;
      _totalCount = result.total;
      
      LogUtil.debug('仓库返回角色数量: ${roles.length}, 总数: $_totalCount');
      
      // 更新角色列表
      if (isLoadMore) {
        // 加载更多时，只有当有新数据时才追加
        if (roles.isNotEmpty) {
          // 使用异步方式添加数据，避免UI卡顿
          await Future.microtask(() {
            // 检查是否有重复数据
            final newRoles = roles.where((newRole) => 
              !recommendedRoles.any((existingRole) => existingRole.id == newRole.id)
            ).toList();
            
            if (newRoles.isNotEmpty) {
              // 使用List.addAll而不是RxList.addAll，减少不必要的UI刷新
              final updatedList = [...recommendedRoles, ...newRoles];
              recommendedRoles.value = updatedList;
              LogUtil.debug('加载更多：添加${newRoles.length}个新角色到列表，当前列表大小: ${recommendedRoles.length}');
              
              // 重新计算是否还有更多数据 - 考虑到已过滤重复项后的实际新增数量
              hasMoreData = _totalCount > recommendedRoles.length;
            } else {
              LogUtil.debug('加载更多：所有返回的角色都已存在，不添加重复数据');
              // 如果全是重复数据，也要重新评估是否还有更多
              hasMoreData = _totalCount > recommendedRoles.length;
            }
          });
        } else {
          LogUtil.debug('加载更多：服务器返回空数据，不更新列表');
          // 如果服务器返回空数据，可能表示没有更多了
          hasMoreData = false;
        }
      } else {
        // 刷新或首次加载时，替换整个列表
        await Future.microtask(() {
          recommendedRoles.assignAll(roles);
          LogUtil.debug('全量刷新：更新整个角色列表，大小: ${recommendedRoles.length}');
          
          // 重新计算是否还有更多数据
          hasMoreData = _totalCount > recommendedRoles.length;
        });
      }
      
      // 更新最大加载页码
      if (request.page > _maxLoadedPage) {
        _maxLoadedPage = request.page;
        LogUtil.debug('更新最大加载页码: $_maxLoadedPage');
      }
      
      // 记录当前分页状态
      LogUtil.debug('当前分页状态: 总数据量=${_totalCount}, 已加载=${recommendedRoles.length}, 最大页码=${_maxLoadedPage}, 是否有更多=${hasMoreData}');
      
      // 添加额外的日志信息，帮助排查问题
      if (isLoadMore) {
        LogUtil.debug('分页加载详情: 当前页=${request.page}, 页大小=${request.size}, 理论总页数=${(_totalCount / request.size).ceil()}');
      }
      
      return true;
    } catch (e) {
      LogUtil.error('从仓库加载推荐角色失败: $e');
      return false;
    } finally {
      // 延迟结束加载状态，确保UI平滑过渡
      if (!isLoadMore) {
        Future.delayed(const Duration(milliseconds: 100), () {
          isLoadingRoles.value = false;
        });
      }
      currentApiRequest.value = ''; // 清除API请求记录
    }
  }
  
  /// 从仓库加载推荐角色列表
  /// 
  /// [forceRefresh] 是否强制刷新缓存
  /// 返回错误信息，成功则返回null
  Future<AppException?> _loadRolesFromRepository({bool forceRefresh = false}) async {
    LogUtil.info('=== 开始加载推荐角色 ===');
    LogUtil.info('forceRefresh: $forceRefresh');
    
    // 防止并发请求
    if (isLoadingRoles.value) {
      LogUtil.debug('已有加载任务在进行中，跳过重复请求');
      return AppException('there is a loading task in progress, skip the duplicate request', code: ErrorCodes.BUSINESS_ERROR);
    }
    
    try {
      isLoadingRoles.value = true;
      
      // 如果强制刷新，先清除仓库缓存
      if (forceRefresh) {
        LogUtil.debug('强制刷新，清除仓库缓存');
        await _repository.clearCache();
      }
      
      // 从仓库获取角色列表，按照接口文档参数
      final params = {
        'page': 1,
        'size': StringsConsts.recommendPageSize,
        'tag': StringsConsts.recommendDefaultTag
      };
      LogUtil.debug('请求参数: $params');
      
      // 记录当前API请求用于调试
      currentApiRequest.value = 'getList with params: $params';
      
      // 调用仓库层方法获取数据，传递forceRefresh参数
      final roles = await _repository.getList(
        params: params,
        forceRefresh: forceRefresh
      );
      
      LogUtil.debug('仓库返回角色数量: ${roles.length}');
      
      // 更新角色列表，无论是否为空都接受结果
      recommendedRoles.assignAll(roles);
      LogUtil.debug('从仓库加载了${roles.length}个推荐角色');
      return null; // 成功返回null
    } catch (e) {
      LogUtil.error('从仓库加载推荐角色失败: $e');
      return ErrorHandler.createAppException(e, 'the role load failed, please try again later');
    } finally {
      isLoadingRoles.value = false;
      currentApiRequest.value = ''; // 清除API请求记录
    }
  }
  
  // 测试网络连接的方法
  Future<bool> testNetworkConnection() async {
    try {
      LogUtil.info('=== 开始测试网络连接 ===');
      final result = await _repository.getList(params: {'page': 1, 'size': 1});
      LogUtil.info('网络测试结果: ${result.length}个角色');
      return result.isNotEmpty;
    } catch (e) {
      LogUtil.error('网络测试失败: $e');
      return false;
    }
  }
  
  /// 刷新角色列表 - 统一的刷新入口
  /// 
  /// [forceRefresh] 是否强制刷新
  /// 返回刷新结果，成功为true
  Future<bool> refreshRoles({bool forceRefresh = false}) async {
    LogUtil.debug('请求刷新推荐角色列表，forceRefresh: $forceRefresh');
    
    // 如果已经在加载中，不要重复刷新
    if (isLoadingRoles.value) {
      LogUtil.debug('已有刷新任务在进行中，跳过本次刷新');
      return false;
    }
    
    // 创建初始分页请求
    final request = PageRequest(page: 1, size: StringsConsts.recommendPageSize);
    
    // 执行刷新
    return await getPagedRoles(request, forceRefresh: forceRefresh);
  }
  
  /// 刷新推荐角色 - 兼容旧版接口
  @deprecated
  Future<bool> refreshRecommendedRoles() async {
    return refreshRoles(forceRefresh: true);
  }
  
  // IRoleProvider接口实现 - 根据ID获取角色信息
  @override
  Future<AiRole?> getRoleById(int roleId) async {
    try {
      // 先从缓存获取
      final cacheKey = '${_cacheKeyPrefix}$roleId';
      final cachedRole = await _cacheManager.get<AiRole>(
        cacheKey,
        fromJson: (json) => AiRole.fromJson(json),
      );
      
      if (cachedRole != null) {
        LogUtil.debug('从缓存获取角色信息，ID: $roleId');
        return cachedRole;
      }
      
      // 从仓库层获取角色信息
      final role = await _repository.getById(roleId);
      if (role != null) {
        // 缓存获取到的角色
        await _cacheManager.set(cacheKey, role);
        return role;
      }
      
      // 从推荐列表中查找
      final listRole = recommendedRoles.firstWhereOrNull((role) => role.id == roleId);
      if (listRole != null) {
        // 缓存找到的角色
        await _cacheManager.set(cacheKey, listRole);
        return listRole;
      }
      
      // 如果没有找到，创建一个默认角色
      final defaultRole = AiRole(
        id: roleId,
        name: 'AI助手',
        description: '默认AI助手',
        avatarUrl: StringsConsts.recommendDefaultAvatarUrl,
        coverUrl: StringsConsts.recommendDefaultCoverUrl,
        tags: ['助手'],
        position: 0,
      );
      
      return defaultRole;
    } catch (e) {
      LogUtil.error('获取角色失败 ID: $roleId, 错误: $e');
      throw ErrorHandler.createAppException(e, 'the role info load failed, please try again later');
    }
  }
  
  // IRoleProvider接口实现 - 获取角色名称
  @override
  Future<String?> getRoleNameById(int roleId) async {
    try {
      // 从仓库层获取角色
      final role = await getRoleById(roleId);
      return role?.name ?? 'AI助手';
    } catch (e) {
      LogUtil.error('获取角色名称失败 ID: $roleId, 错误: $e');
      return 'AI助手';
    }
  }
  
  // IRoleProvider接口实现 - 获取角色头像URL
  @override
  Future<String?> getAvatarUrlById(int roleId) async {
    try {
      // 先从缓存获取
      final cacheKey = '${_avatarCacheKeyPrefix}$roleId';
      final cachedUrl = await _cacheManager.get<String>(cacheKey);
      
      if (cachedUrl != null) {
        return cachedUrl;
      }
      
      // 从角色对象获取
      final role = await getRoleById(roleId);
      if (role?.avatarUrl != null) {
        // 缓存头像URL
        await _cacheManager.set(cacheKey, role!.avatarUrl);
        return role.avatarUrl;
      }
      
      return StringsConsts.recommendDefaultAvatarUrl;
    } catch (e) {
      LogUtil.error('获取角色头像失败 ID: $roleId, 错误: $e');
      return StringsConsts.recommendDefaultAvatarUrl;
    }
  }
  
  // IRoleProvider接口实现 - 获取角色封面URL
  @override
  Future<String?> getCoverUrlById(int roleId) async {
    try {
      // 先从缓存获取
      final cacheKey = '${_coverCacheKeyPrefix}$roleId';
      final cachedUrl = await _cacheManager.get<String>(cacheKey);
      
      if (cachedUrl != null) {
        return cachedUrl;
      }
      
      // 从角色对象获取
      final role = await getRoleById(roleId);
      if (role?.coverUrl != null) {
        // 缓存封面URL
        await _cacheManager.set(cacheKey, role!.coverUrl);
        return role.coverUrl;
      }
      
      return StringsConsts.recommendDefaultCoverUrl;
    } catch (e) {
      LogUtil.error('获取角色封面失败 ID: $roleId, 错误: $e');
      return StringsConsts.recommendDefaultCoverUrl;
    }
  }
  
  // IRoleProvider接口实现 - 获取角色描述
  @override
  Future<String?> getDescriptionById(int roleId) async {
    try {
      final role = await getRoleById(roleId);
      return role?.description;
    } catch (e) {
      LogUtil.error('获取角色描述失败 ID: $roleId, 错误: $e');
      return '默认AI助手描述';
    }
  }
  
  // IRoleProvider接口实现 - 获取所有角色信息的流
  @override
  Stream<List<AiRole>> getRoles() {
    // 如果角色列表为空且没有正在加载，触发加载
    if (recommendedRoles.isEmpty && !isLoadingRoles.value) {
      LogUtil.debug('角色列表为空且未在加载中，异步加载角色');
      // 异步加载，不阻塞当前流
      Future.microtask(() => _loadRolesFromRepository(forceRefresh: false));
    }
    
    // 返回角色列表流
    return recommendedRoles.stream;
  }
  
  // 获取角色列表
  List<AiRole> getRolesList() {
    return recommendedRoles.toList();
  }
  
  // 清除缓存
  Future<void> clearCache() async {
    try {
      // 清除仓库缓存
      await _repository.clearCache();
      
      // 清除本地缓存
      final keys = [
        _cacheKeyPrefix,
        _avatarCacheKeyPrefix,
        _coverCacheKeyPrefix
      ];
      
      for (final prefix in keys) {
        await _cacheManager.clear(strategy: CacheStrategy.both);
      }
      
      LogUtil.debug('已清除推荐角色缓存');
    } catch (e) {
      LogUtil.error('清除推荐角色缓存失败: $e');
      throw ErrorHandler.createAppException(e, 'the cache clear failed, please try again later');
    }
  }
  
  @override
  Future<AiRole?> getNextRecommendRole(int currentRoleId) async {
    try {
      // 记录当前角色列表的大小
      LogUtil.debug('获取下一个推荐角色, 当前列表大小: ${recommendedRoles.length}, 当前角色ID: $currentRoleId');
      
      // 如果角色列表为空或者太小，可能需要刷新
      if (recommendedRoles.isEmpty || (totalCount > 0 && recommendedRoles.length < totalCount)) {
        LogUtil.info('角色列表不完整(${recommendedRoles.length}/$totalCount)，尝试获取更多角色');
        // 如果列表为空，先加载第一页
        if (recommendedRoles.isEmpty) {
          await refreshRoles(forceRefresh: true);
        } 
        // 如果列表已有数据但不完整，尝试加载更多页
        else if (hasMoreData) {
          final request = PageRequest(
            page: _maxLoadedPage + 1, 
            size: StringsConsts.recommendPageSize
          );
          await getPagedRoles(request, isLoadMore: true);
        }
        LogUtil.debug('刷新后角色列表大小: ${recommendedRoles.length}');
      }
      
      // 获取当前角色在列表中的索引
      final currentRoleIndex = recommendedRoles
        .indexWhere((role) => role.id == currentRoleId);
      
      // 如果找不到当前角色，返回第一个角色
      if (currentRoleIndex == -1) {
        LogUtil.debug('未找到当前角色ID: $currentRoleId，返回第一个角色');
        return recommendedRoles.isNotEmpty 
          ? recommendedRoles[0] 
          : null;
      }
      
      // 获取下一个角色，如果是最后一个则循环到第一个
      final nextIndex = (currentRoleIndex + 1) % recommendedRoles.length;
      LogUtil.debug('当前角色索引: $currentRoleIndex, 下一个角色索引: $nextIndex, 总数: ${recommendedRoles.length}');
      return recommendedRoles[nextIndex];
    } catch (e) {
      LogUtil.error('获取下一个推荐角色失败: $e');
      return null;
    }
  }
  
  @override
  Future<AiRole?> getPreviousRecommendRole(int currentRoleId) async {
    try {
      // 记录当前角色列表的大小
      LogUtil.debug('获取上一个推荐角色, 当前列表大小: ${recommendedRoles.length}, 当前角色ID: $currentRoleId');
      
      // 如果角色列表为空或者太小，可能需要刷新
      if (recommendedRoles.isEmpty || (totalCount > 0 && recommendedRoles.length < totalCount)) {
        LogUtil.info('角色列表不完整(${recommendedRoles.length}/$totalCount)，尝试获取更多角色');
        // 如果列表为空，先加载第一页
        if (recommendedRoles.isEmpty) {
          await refreshRoles(forceRefresh: true);
        } 
        // 如果列表已有数据但不完整，尝试加载更多页
        else if (hasMoreData) {
          final request = PageRequest(
            page: _maxLoadedPage + 1, 
            size: StringsConsts.recommendPageSize
          );
          await getPagedRoles(request, isLoadMore: true);
        }
        LogUtil.debug('刷新后角色列表大小: ${recommendedRoles.length}');
      }
      
      // 获取当前角色在列表中的索引
      final currentRoleIndex = recommendedRoles
        .indexWhere((role) => role.id == currentRoleId);
      
      // 如果找不到当前角色，返回最后一个角色
      if (currentRoleIndex == -1) {
        LogUtil.debug('未找到当前角色ID: $currentRoleId，返回最后一个角色');
        return recommendedRoles.isNotEmpty 
          ? recommendedRoles.last 
          : null;
      }
      
      // 获取上一个角色，如果是第一个则循环到最后一个
      final previousIndex = (currentRoleIndex - 1 + recommendedRoles.length) % recommendedRoles.length;
      LogUtil.debug('当前角色索引: $currentRoleIndex, 上一个角色索引: $previousIndex, 总数: ${recommendedRoles.length}');
      return recommendedRoles[previousIndex];
    } catch (e) {
      LogUtil.error('获取上一个推荐角色失败: $e');
      return null;
    }
  }
  
  // 实现新增的会话角色相关方法
  // 确保会话角色列表已加载
  Future<void> _ensureSessionRolesLoaded() async {
    if (sessionRoles.isEmpty) {
      // 如果没有会话角色，尝试从推荐列表中获取
      if (recommendedRoles.isEmpty) {
        // 如果推荐列表也为空，加载推荐角色
        await _loadRolesFromRepository(forceRefresh: false);
      }
      
      // 将推荐角色作为会话角色
      sessionRoles.assignAll(recommendedRoles);
      LogUtil.info('已将推荐角色列表(${recommendedRoles.length}个)用作会话角色列表');
    }
  }
  
  @override
  Future<AiRole?> getNextSessionRole(int currentRoleId) async {
    try {
      // 确保会话角色列表有数据
      await _ensureSessionRolesLoaded();
      
      // 使用与getNextRecommendRole相同的逻辑，但操作sessionRoles而不是recommendedRoles
      LogUtil.debug('获取下一个会话角色, 当前列表大小: ${sessionRoles.length}, 当前角色ID: $currentRoleId');
      
      if (sessionRoles.isEmpty) {
        LogUtil.info('没有活跃的会话角色');
        return null;
      }
      
      // 获取当前角色在列表中的索引
      final currentIndex = sessionRoles.indexWhere((role) => role.id == currentRoleId);
      
      // 如果找不到当前角色，返回第一个角色
      if (currentIndex == -1) {
        LogUtil.debug('未找到当前角色ID: $currentRoleId，返回第一个角色');
        return sessionRoles.isNotEmpty ? sessionRoles[0] : null;
      }
      
      // 获取下一个角色，如果是最后一个则循环到第一个
      final nextIndex = (currentIndex + 1) % sessionRoles.length;
      AiRole nextRole = sessionRoles[nextIndex];
      
      // 确保获取最新的会话ID信息
      final updatedRole = await getRoleById(nextRole.id);
      if (updatedRole != null) {
        LogUtil.debug('从缓存获取角色的会话ID: ${updatedRole.conversationId}');
        nextRole = updatedRole;
      } else {
        // 如果缓存中没有找到，检查SessionBindingService
        if (Get.isRegistered<SessionBindingService>()) {
          final bindingService = Get.find<SessionBindingService>();
          final conversationId = bindingService.getConversationIdForRole(nextRole.id);
          if (conversationId != null) {
            LogUtil.debug('从SessionBindingService获取会话ID: $conversationId');
            nextRole = nextRole.copyWith(conversationId: conversationId);
          }
        }
      }
      
      LogUtil.debug('当前角色索引: $currentIndex, 下一个角色索引: $nextIndex, 总数: ${sessionRoles.length}');
      return nextRole;
    } catch (e) {
      LogUtil.error('获取下一个会话角色失败: $e');
      return null;
    }
  }
  
  @override
  Future<AiRole?> getPreviousSessionRole(int currentRoleId) async {
    try {
      // 确保会话角色列表有数据
      await _ensureSessionRolesLoaded();
      
      // 使用与getPreviousRecommendRole相同的逻辑，但操作sessionRoles而不是recommendedRoles
      LogUtil.debug('获取上一个会话角色, 当前列表大小: ${sessionRoles.length}, 当前角色ID: $currentRoleId');
      
      if (sessionRoles.isEmpty) {
        LogUtil.info('没有活跃的会话角色');
        return null;
      }
      
      // 获取当前角色在列表中的索引
      final currentIndex = sessionRoles.indexWhere((role) => role.id == currentRoleId);
      
      // 如果找不到当前角色，返回最后一个角色
      if (currentIndex == -1) {
        LogUtil.debug('未找到当前角色ID: $currentRoleId，返回最后一个角色');
        return sessionRoles.isNotEmpty ? sessionRoles.last : null;
      }
      
      // 获取上一个角色，如果是第一个则循环到最后一个
      final previousIndex = (currentIndex - 1 + sessionRoles.length) % sessionRoles.length;
      AiRole previousRole = sessionRoles[previousIndex];
      
      // 确保获取最新的会话ID信息
      final updatedRole = await getRoleById(previousRole.id);
      if (updatedRole != null) {
        LogUtil.debug('从缓存获取角色的会话ID: ${updatedRole.conversationId}');
        previousRole = updatedRole;
      } else {
        // 如果缓存中没有找到，检查SessionBindingService
        if (Get.isRegistered<SessionBindingService>()) {
          final bindingService = Get.find<SessionBindingService>();
          final conversationId = bindingService.getConversationIdForRole(previousRole.id);
          if (conversationId != null) {
            LogUtil.debug('从SessionBindingService获取会话ID: $conversationId');
            previousRole = previousRole.copyWith(conversationId: conversationId);
          }
        }
      }
      
      LogUtil.debug('当前角色索引: $currentIndex, 上一个角色索引: $previousIndex, 总数: ${sessionRoles.length}');
      return previousRole;
    } catch (e) {
      LogUtil.error('获取上一个会话角色失败: $e');
      return null;
    }
  }
  
  /// 为角色添加标签
  Future<bool> addTagToRole(int roleId, String tag) async {
    // 实现逻辑...
    return false;
  }
  
  /// 更新角色的会话ID
  Future<void> updateRoleConversationId(int roleId, int conversationId, AiRole updatedRole) async {
    try {
      // 更新内存中的角色列表
      int index = recommendedRoles.indexWhere((role) => role.id == roleId);
      if (index >= 0) {
        recommendedRoles[index] = updatedRole;
        recommendedRoles.refresh(); // 触发UI更新
        LogUtil.debug('已更新内存中角色列表的会话ID: roleId=$roleId, conversationId=$conversationId');
      } else {
        LogUtil.debug('角色不在推荐列表中，跳过内存更新: roleId=$roleId');
      }

      // 更新缓存，使用延迟写入避免频繁操作
      Future.delayed(const Duration(milliseconds: 50), () async {
        try {
          final cacheKey = '${_cacheKeyPrefix}$roleId';
          await _cacheManager.set(cacheKey, updatedRole, strategy: CacheStrategy.both);
          LogUtil.debug('角色会话ID缓存更新完成: roleId=$roleId, conversationId=$conversationId');
        } catch (e) {
          LogUtil.error('延迟更新角色会话ID缓存失败: roleId=$roleId, 错误: $e');
        }
      });

    } catch (e) {
      LogUtil.error('更新角色会话ID失败: roleId=$roleId, 错误: $e');
    }
  }

  @override
  void onClose() {
    try {
      LogUtil.debug('RecommendService开始清理资源');

      // 清空角色列表数据
      recommendedRoles.clear();
      sessionRoles.clear();

      // 重置状态变量
      isLoadingRoles.value = false;
      hasMoreData = true;
      _totalCount = 0;
      _maxLoadedPage = 1;
      currentApiRequest.value = '';
      isHotDataPreloaded.value = false;

      // 关闭响应式变量
      recommendedRoles.close();
      sessionRoles.close();
      isLoadingRoles.close();
      currentApiRequest.close();
      isHotDataPreloaded.close();

      LogUtil.debug('RecommendService资源清理完成');
    } catch (e) {
      LogUtil.error('RecommendService清理资源失败: $e');
    }

    super.onClose();
  }
}